import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart';
import 'package:universal_html/html.dart' as html;

import '../../../../../models/finance/bill_model.dart';
import '../../../../../models/invoice_model.dart';
import '../../../../../firebase_service/finance/bill_firebase_service.dart';
import '../../../../../core/utils/constants/constants.dart';

class BillDetailController extends GetxController {
  final BillFirebaseService _billFirebaseService = BillFirebaseService();

  // Observable variables
  final RxBool isLoadingInvoices = false.obs;
  final RxBool isExporting = false.obs;
  final RxList<InvoiceModel> linkedInvoices = <InvoiceModel>[].obs;

  // Current bill
  BillModel? currentBill;

  /// Initialize the controller with bill data
  void initializeBill(BillModel bill) {
    currentBill = bill;
    loadLinkedInvoices();
  }

  /// Load linked invoices for the current bill
  Future<void> loadLinkedInvoices() async {
    if (currentBill == null) return;

    isLoadingInvoices.value = true;
    try {
      log('Loading linked invoices for bill: ${currentBill!.billNumber}');
      log('Linked invoice IDs: ${currentBill!.linkedInvoiceIds}');

      final invoices = await _billFirebaseService.getLinkedInvoices(
        uid: currentBill!.companyUid,
        invoiceIds: currentBill!.linkedInvoiceIds,
      );

      linkedInvoices.value = invoices;
      log('Successfully loaded ${invoices.length} linked invoices');
    } catch (e) {
      log('Error loading linked invoices: $e');
      Get.snackbar(
        'Error',
        'Failed to load linked invoices: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoadingInvoices.value = false;
    }
  }

  /// Export bill data to Excel
  Future<void> exportToExcel() async {
    if (currentBill == null || linkedInvoices.isEmpty) {
      Get.snackbar(
        'Error',
        'No data to export',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    isExporting.value = true;

    try {
      log('Generating Excel for bill: ${currentBill!.billNumber}');

      // Create Excel workbook
      final excel = Excel.createExcel();
      final sheet = excel['Bill Report'];

      // Remove default sheet if it exists
      if (excel.sheets.containsKey('Sheet1')) {
        excel.delete('Sheet1');
      }

      // Calculate the starting row for data (3.5 inches = approximately 25 rows in Excel)
      const int topMarginRows = 25;
      const int headerRow = topMarginRows;
      const int dataStartRow = topMarginRows + 1;

      // Add bill number and invoice count header at the top margin area
      final billNumberCell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: 0, rowIndex: topMarginRows - 5));
      billNumberCell.value = TextCellValue('BILL # ${currentBill!.billNumber}');
      billNumberCell.cellStyle = CellStyle(
        bold: true,
        fontSize: 14,
        fontColorHex: ExcelColor.black,
      );

      // Add total invoice count (right side)
      final invoiceCountCell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: 10, rowIndex: topMarginRows - 5));
      invoiceCountCell.value =
          TextCellValue('Total Invoices: ${linkedInvoices.length}');
      invoiceCountCell.cellStyle = CellStyle(
        bold: true,
        fontSize: 12,
        fontColorHex: ExcelColor.black,
      );

      // Define headers
      final headers = [
        'SN.',
        'Lifting Date',
        'Truck No',
        'Bilty No',
        'Convey Note Number',
        'Product Name',
        'Product TAS NO',
        'Destination',
        'No of Bags',
        'Weight',
        'KM',
        'District',
        'HMT Rates (Non Fuel Inc WHT)',
        '100% Amount',
        '80% Amount',
        'Net Amount',
      ];

      // Header style
      final headerStyle = CellStyle(
        bold: true,
        fontSize: 11,
        fontColorHex: ExcelColor.black,
      );

      // Add headers to the header row (after top margin)
      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: i, rowIndex: headerRow));
        cell.value = TextCellValue(headers[i]);
        cell.cellStyle = headerStyle;
      }

      // Add data rows
      double totalTons = 0.0;
      double total100Amount = 0.0;
      double total80Amount = 0.0;

      for (int i = 0; i < linkedInvoices.length; i++) {
        final invoice = linkedInvoices[i];
        final rowIndex = dataStartRow + i;

        // Calculate total weight in tons
        final totalWeightTons =
            (invoice.numberOfBags * invoice.weightPerBag) / 1000;

        // Format weight with dynamic decimal places
        final totalWeightFormatted = totalWeightTons % 1 == 0
            ? totalWeightTons.toInt().toString()
            : totalWeightTons
                .toStringAsFixed(2)
                .replaceAll(RegExp(r'\.?0+$'), '');

        // Use default rate for now - this should be replaced with actual slab calculation
        final hmtRate = 150.0;
        final amount100 =
            MonetaryRounding.roundHalfUp(totalWeightTons * hmtRate);
        final amount80 = MonetaryRounding.roundHalfUp(amount100 * 0.80);
        final netAmount = amount100;

        // Add to totals
        totalTons += totalWeightTons;
        total100Amount += amount100;
        total80Amount += amount80;

        final rowData = [
          (i + 1).toString(),
          invoice.orderDate != null
              ? DateFormat('dd/MM/yyyy').format(invoice.orderDate!)
              : '',
          invoice.truckNumber,
          invoice.biltyNumber,
          invoice.conveyNoteNumber,
          invoice.productName,
          invoice.tasNumber,
          invoice.stationName,
          invoice.numberOfBags.toString(),
          totalWeightFormatted,
          invoice.distanceInKilometers.toString(),
          invoice.districtName,
          _formatMonetaryAmount(hmtRate),
          _formatMonetaryAmount(amount100),
          _formatMonetaryAmount(amount80),
          _formatMonetaryAmount(netAmount),
        ];

        for (int j = 0; j < rowData.length; j++) {
          final cell = sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: j, rowIndex: rowIndex));
          cell.value = TextCellValue(rowData[j]);
        }
      }

      // Add summary section
      final summaryStartRow = dataStartRow + linkedInvoices.length + 1;

      // Summary row
      final summaryData = [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Tons',
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(totalTons)),
        'Bill Amount Exclusive of GST (100%)',
        '',
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(total100Amount)),
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(total80Amount)),
        _formatMonetaryAmount(MonetaryRounding.roundHalfUp(total100Amount)),
      ];

      for (int j = 0; j < summaryData.length; j++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: j, rowIndex: summaryStartRow));
        cell.value = TextCellValue(summaryData[j]);
        cell.cellStyle = CellStyle(bold: true);
      }

      // Set optimized column widths for print-friendly layout
      final columnWidths = [
        6.0,
        10.0,
        9.0,
        9.0,
        12.0,
        10.0,
        10.0,
        11.0,
        8.0,
        8.0,
        6.0,
        10.0,
        12.0,
        10.0,
        10.0,
        10.0,
      ];

      for (int i = 0; i < headers.length && i < columnWidths.length; i++) {
        sheet.setColumnWidth(i, columnWidths[i]);
      }

      // Generate Excel bytes
      final excelBytes = excel.encode();
      if (excelBytes == null) {
        throw Exception('Failed to generate Excel file');
      }

      // Generate filename
      final dateFormat = DateFormat('yyyy-MM-dd');
      final fileName =
          'Bill_${currentBill!.billNumber}_${dateFormat.format(DateTime.now())}.xlsx';

      // Download file for web
      if (kIsWeb) {
        await _downloadExcelWeb(Uint8List.fromList(excelBytes), fileName);
      } else {
        throw UnimplementedError(
            'Mobile/Desktop Excel download not implemented');
      }

      log('Excel file generated successfully: $fileName');

      // Show success message
      Get.snackbar(
        'Success',
        'Excel file "$fileName" has been downloaded',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      log('Error generating Excel file: $e');
      Get.snackbar(
        'Error',
        'Failed to generate Excel file: $e',
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isExporting.value = false;
    }
  }

  /// Download Excel file for web platform
  Future<void> _downloadExcelWeb(Uint8List excelBytes, String fileName) async {
    try {
      final blob = html.Blob([excelBytes],
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..click();

      html.Url.revokeObjectUrl(url);

      log('Excel file downloaded successfully on web: $fileName');
    } catch (e) {
      log('Error downloading Excel file on web: $e');
      rethrow;
    }
  }

  /// Format monetary amount for display
  String _formatMonetaryAmount(double amount) {
    final rounded = MonetaryRounding.roundHalfUp(amount);
    if (rounded == 0) return '0';

    // Remove unnecessary decimal places
    if (rounded % 1 == 0) {
      return rounded.toInt().toString();
    } else {
      return rounded.toStringAsFixed(2).replaceAll(RegExp(r'\.?0+$'), '');
    }
  }

  @override
  void onClose() {
    linkedInvoices.clear();
    currentBill = null;
    super.onClose();
  }
}
