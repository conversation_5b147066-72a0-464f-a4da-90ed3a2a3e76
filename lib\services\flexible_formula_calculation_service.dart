import 'dart:developer';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';

/// Service for executing flexible user-defined calculation formulas
class FlexibleFormulaCalculationService {
  /// Calculate invoice amount using a custom formula
  /// Returns the calculated amount or null if calculation fails
  static double? calculateWithFormula({
    required InvoiceModel invoice,
    required CalculationFormulaModel formula,
    required Map<String, double> customColumnValues,
  }) {
    try {
      log('Starting formula calculation for invoice ${invoice.tasNumber} using formula: ${formula.formulaName}');

      // Validate formula
      if (!formula.isValid()) {
        log('Invalid formula structure');
        return null;
      }

      // Initialize variable context with invoice data and custom columns
      final variables = _initializeVariables(invoice, customColumnValues);
      log('Initialized variables: $variables');

      // Execute formula steps sequentially
      for (int i = 0; i < formula.steps.length; i++) {
        final step = formula.steps[i];
        log('Executing step ${i + 1}: ${step.stepName} - ${step.formula}');

        final stepResult = _executeFormulaStep(step, variables);
        if (stepResult == null) {
          log('Failed to execute step: ${step.stepName}');
          return null;
        }

        // Store step result in variables for use in subsequent steps
        variables[step.resultVariable] = stepResult;
        log('Step ${i + 1} result: ${step.resultVariable} = $stepResult');
      }

      // Get final result
      final finalResult = variables[formula.finalResultVariable];
      if (finalResult == null) {
        log('Final result variable ${formula.finalResultVariable} not found');
        return null;
      }

      // Apply monetary rounding to the final result
      final roundedResult = MonetaryRounding.roundHalfUp(finalResult);

      log('Formula calculation completed. Final result: $finalResult → rounded to: $roundedResult');
      return roundedResult;
    } catch (e) {
      log('Error in formula calculation: $e');
      return null;
    }
  }

  /// Initialize variables with invoice data and custom column values
  static Map<String, double> _initializeVariables(
      InvoiceModel invoice, Map<String, double> customColumnValues) {
    final totalWeightKg = invoice.numberOfBags * invoice.weightPerBag;
    final totalWeightTons = totalWeightKg / 1000;

    final variables = {
      FormulaVariables.numberOfBags: invoice.numberOfBags.toDouble(),
      FormulaVariables.weightPerBag: invoice.weightPerBag,
      FormulaVariables.totalWeightKg: totalWeightKg,
      FormulaVariables.totalWeightTons: totalWeightTons,
      FormulaVariables.distanceInKilometers:
          invoice.distanceInKilometers.toDouble(),
    };

    // Add custom column values
    variables.addAll(customColumnValues);

    return variables;
  }

  /// Execute a single formula step
  static double? _executeFormulaStep(
      FormulaStepModel step, Map<String, double> variables) {
    try {
      // Replace variables in formula with actual values
      String processedFormula = step.formula;

      // Replace each variable with its value
      for (final entry in variables.entries) {
        final variableName = entry.key;
        final variableValue = entry.value;
        processedFormula =
            processedFormula.replaceAll(variableName, variableValue.toString());
      }

      log('Original formula: ${step.formula}');
      log('Processed formula: $processedFormula');

      // Convert display operators to calculation operators
      processedFormula = _convertOperators(processedFormula);
      log('Converted formula: $processedFormula');

      // Evaluate the mathematical expression
      final result = evaluateExpression(processedFormula);
      log('Evaluation result: $result');

      return result;
    } catch (e) {
      log('Error executing formula step ${step.stepName}: $e');
      return null;
    }
  }

  /// Convert display operators (×, ÷) to calculation operators (*, /)
  static String _convertOperators(String formula) {
    return formula
        .replaceAll(FormulaOperations.multiplication, '*')
        .replaceAll(FormulaOperations.division, '/');
  }

  /// Evaluate a mathematical expression
  /// This is a simple expression evaluator that handles basic arithmetic
  static double? evaluateExpression(String expression) {
    try {
      // Remove spaces
      expression = expression.replaceAll(' ', '');

      // Simple expression evaluation using recursive descent parser
      final parser = _ExpressionParser(expression);
      return parser.parse();
    } catch (e) {
      log('Error evaluating expression: $expression, Error: $e');
      return null;
    }
  }

  /// Validate a formula expression syntax
  static bool validateFormulaExpression(
      String formula, List<String> availableVariables) {
    try {
      // Check for balanced parentheses
      int parenthesesCount = 0;
      for (int i = 0; i < formula.length; i++) {
        if (formula[i] == '(') parenthesesCount++;
        if (formula[i] == ')') parenthesesCount--;
        if (parenthesesCount < 0) return false; // Closing before opening
      }
      if (parenthesesCount != 0) return false; // Unbalanced parentheses

      // Check if all variables in formula are available
      for (final variable in availableVariables) {
        if (formula.contains(variable)) {
          // Variable is used, which is good
        }
      }

      // Try to parse the expression with dummy values
      String testFormula = formula;
      for (final variable in availableVariables) {
        testFormula = testFormula.replaceAll(variable, '1.0');
      }

      testFormula = _convertOperators(testFormula);
      final result = evaluateExpression(testFormula);

      return result != null;
    } catch (e) {
      return false;
    }
  }

  /// Test a formula with sample data
  static Map<String, dynamic> testFormula({
    required CalculationFormulaModel formula,
    Map<String, double>? testVariables,
  }) {
    try {
      // Use default test values if not provided
      final variables = testVariables ??
          {
            FormulaVariables.numberOfBags: 100.0,
            FormulaVariables.weightPerBag: 50.0,
            FormulaVariables.totalWeightKg: 5000.0,
            FormulaVariables.totalWeightTons: 5.0,
            FormulaVariables.distanceInKilometers: 150.0,
            // Custom columns would be added here based on slab configuration
            'sampleRate': 2.5,
          };

      final stepResults = <String, double>{};
      final stepDetails = <Map<String, dynamic>>[];

      // Execute each step
      for (int i = 0; i < formula.steps.length; i++) {
        final step = formula.steps[i];
        final stepResult = _executeFormulaStep(step, variables);

        if (stepResult == null) {
          return {
            'success': false,
            'error': 'Failed to execute step: ${step.stepName}',
            'stepResults': stepResults,
            'stepDetails': stepDetails,
          };
        }

        stepResults[step.resultVariable] = stepResult;
        variables[step.resultVariable] = stepResult;

        stepDetails.add({
          'stepName': step.stepName,
          'formula': step.formula,
          'result': stepResult,
          'resultVariable': step.resultVariable,
        });
      }

      final finalResult = variables[formula.finalResultVariable];

      return {
        'success': true,
        'finalResult': finalResult,
        'stepResults': stepResults,
        'stepDetails': stepDetails,
        'testVariables': testVariables ?? variables,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'stepResults': <String, double>{},
        'stepDetails': <Map<String, dynamic>>[],
      };
    }
  }
}

/// Simple expression parser for basic arithmetic
class _ExpressionParser {
  final String expression;
  int position = 0;

  _ExpressionParser(this.expression);

  double parse() {
    final result = parseExpression();
    if (position < expression.length) {
      throw Exception('Unexpected character at position $position');
    }
    return result;
  }

  double parseExpression() {
    double result = parseTerm();

    while (position < expression.length) {
      final char = expression[position];
      if (char == '+') {
        position++;
        result += parseTerm();
      } else if (char == '-') {
        position++;
        result -= parseTerm();
      } else {
        break;
      }
    }

    return result;
  }

  double parseTerm() {
    double result = parseFactor();

    while (position < expression.length) {
      final char = expression[position];
      if (char == '*') {
        position++;
        result *= parseFactor();
      } else if (char == '/') {
        position++;
        final divisor = parseFactor();
        if (divisor == 0) throw Exception('Division by zero');
        result /= divisor;
      } else {
        break;
      }
    }

    return result;
  }

  double parseFactor() {
    if (position >= expression.length) {
      throw Exception('Unexpected end of expression');
    }

    final char = expression[position];

    if (char == '(') {
      position++;
      final result = parseExpression();
      if (position >= expression.length || expression[position] != ')') {
        throw Exception('Missing closing parenthesis');
      }
      position++;
      return result;
    }

    if (char == '-') {
      position++;
      return -parseFactor();
    }

    return parseNumber();
  }

  double parseNumber() {
    final start = position;

    while (position < expression.length) {
      final char = expression[position];
      if (char.contains(RegExp(r'[0-9.]'))) {
        position++;
      } else {
        break;
      }
    }

    if (start == position) {
      throw Exception('Expected number at position $position');
    }

    final numberStr = expression.substring(start, position);
    return double.parse(numberStr);
  }
}
