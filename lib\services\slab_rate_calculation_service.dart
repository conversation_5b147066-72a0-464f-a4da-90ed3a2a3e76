import 'dart:developer';
import 'package:logestics/core/utils/constants/constants.dart';
import 'package:logestics/features/slab/domain/usecases/get_active_slabs_for_district_use_case.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

/// Service for calculating billing amounts using slab rates
class SlabRateCalculationService {
  final GetActiveSlabsForDistrictUseCase getActiveSlabsForDistrictUseCase;

  SlabRateCalculationService({
    required this.getActiveSlabsForDistrictUseCase,
  });

  /// Calculate billing amount for a single invoice using slab rates
  /// Returns the calculated amount or null if no applicable slab rate is found
  Future<double?> calculateInvoiceAmount({
    required InvoiceModel invoice,
    String rateType = 'hmtRate', // Default to HMT rate
    SlabModel? selectedSlab, // Optional specific slab to use for calculation
  }) async {
    try {
      SlabModel? slab;

      // If a specific slab is provided, use it directly
      if (selectedSlab != null) {
        slab = selectedSlab;
        log('Using selected slab: ${slab.slabName} for invoice ${invoice.tasNumber}');
      } else {
        // Use belongsToDate, fallback to orderDate, then createdAt for rate calculation
        final invoiceDate =
            invoice.belongsToDate ?? invoice.orderDate ?? invoice.createdAt;

        // Get active slabs for the invoice's district and date
        final result = await getActiveSlabsForDistrictUseCase.call(
          districtId: invoice.districtId,
          date: invoiceDate,
        );

        final slabResult = result.fold(
          (failure) {
            log('Error getting slab rates for invoice ${invoice.tasNumber}: ${failure.message}');
            return null;
          },
          (slabs) {
            if (slabs.isEmpty) {
              log('No active slab rates found for district ${invoice.districtName} on $invoiceDate');
              return null;
            }

            // Use the most recent slab (slabs are ordered by creation date)
            return slabs.first;
          },
        );

        if (slabResult == null) {
          return null;
        }
        slab = slabResult;
      }

      final rate = slab.getRateForDistrict(invoice.districtId);

      if (rate == null) {
        log('No rate found for district ${invoice.districtName} in slab ${slab.slabName}');
        return null;
      }

      // Calculate amount based on rate type
      // Priority: Custom columns > Legacy fields (for clean slate implementation)
      double rateValue;

      // First check if it's a custom column (prioritize custom columns)
      final customValue = rate.getCustomValue(rateType);
      if (customValue != null &&
          customValue is num &&
          customValue.toDouble() > 0) {
        rateValue = customValue.toDouble();
        log('Using custom column value for $rateType: $rateValue');
      } else {
        // Fallback to legacy fields only if custom column not found or is zero
        switch (rateType.toLowerCase()) {
          case 'hmtrate':
          case 'hmt':
            rateValue = rate.hmtRate;
            break;
          case 'nonfuelrate':
          case 'nonfuel':
            rateValue = rate.nonFuelRate;
            break;
          default:
            // If no custom column found and not a legacy field, try to find any non-zero custom column
            final nonZeroCustomColumns = rate.customColumns.entries
                .where((entry) =>
                    entry.value is num && (entry.value as num).toDouble() > 0)
                .toList();

            if (nonZeroCustomColumns.isNotEmpty) {
              // Use the first non-zero custom column as fallback
              rateValue = (nonZeroCustomColumns.first.value as num).toDouble();
              log('Using first available custom column ${nonZeroCustomColumns.first.key}: $rateValue');
            } else {
              log('No valid rate found for $rateType, using fallback rate');
              return null; // Let the calling method handle fallback
            }
        }
      }

      // Check if slab has custom formula
      if (slab.hasCustomFormula) {
        log('Using custom formula: ${slab.calculationFormula!.formulaName}');

        // Prepare custom column values for formula calculation
        final customColumnValues = <String, double>{};

        // Add the selected rate value with appropriate variable name
        final sanitizedRateType = FormulaVariables.sanitizeColumnName(rateType);
        customColumnValues[sanitizedRateType] = rateValue;

        // Add all other custom column values from the rate
        for (final entry in rate.customColumns.entries) {
          if (entry.value is num) {
            final sanitizedColumnName =
                FormulaVariables.sanitizeColumnName(entry.key);
            customColumnValues[sanitizedColumnName] =
                (entry.value as num).toDouble();
          }
        }

        final formulaResult =
            FlexibleFormulaCalculationService.calculateWithFormula(
          invoice: invoice,
          formula: slab.calculationFormula!,
          customColumnValues: customColumnValues,
        );

        if (formulaResult != null) {
          final roundedResult = MonetaryRounding.roundHalfUp(formulaResult);
          log('Custom formula calculation successful: $formulaResult → rounded to: $roundedResult');
          return roundedResult;
        } else {
          log('Custom formula calculation failed, falling back to standard calculation');
        }
      }

      // Standard calculation: (numberOfBags × weightPerBag × distanceInKilometers × rate) / 1000
      // Dividing by 1000 to convert from kg to tons for rate calculation
      final totalWeightKg = invoice.numberOfBags * invoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final calculatedAmount =
          totalWeightTons * invoice.distanceInKilometers * rateValue;

      // Apply monetary rounding to the final amount
      final roundedAmount = MonetaryRounding.roundHalfUp(calculatedAmount);

      log('Invoice ${invoice.tasNumber}: ${totalWeightTons.toStringAsFixed(2)} tons × ${invoice.distanceInKilometers} km × $rateValue rate = ${calculatedAmount.toStringAsFixed(2)} → rounded to: $roundedAmount');

      return roundedAmount;
    } catch (e) {
      log('Unexpected error calculating invoice amount: $e');
      return null;
    }
  }

  /// Calculate total billing amount for multiple invoices using slab rates
  /// Returns a map with total amount and breakdown by invoice
  Future<SlabCalculationResult> calculateBatchInvoiceAmounts({
    required List<InvoiceModel> invoices,
    String rateType = 'hmtRate',
    double fallbackRate = 0.1, // Fallback rate if no slab rate is found
    SlabModel?
        selectedSlab, // Optional specific slab to use for all calculations
  }) async {
    double totalAmount = 0.0;
    final Map<String, double> invoiceAmounts = {};
    final Map<String, String> calculationDetails = {};
    int slabRateUsedCount = 0;
    int fallbackRateUsedCount = 0;

    for (final invoice in invoices) {
      final slabAmount = await calculateInvoiceAmount(
        invoice: invoice,
        rateType: rateType,
        selectedSlab: selectedSlab,
      );

      double invoiceAmount;
      String calculationMethod;

      if (slabAmount != null) {
        // slabAmount is already rounded from calculateInvoiceAmount
        invoiceAmount = slabAmount;
        calculationMethod = 'Slab Rate';
        slabRateUsedCount++;
      } else {
        // Fallback to the original calculation method
        final fallbackAmount = invoice.numberOfBags *
            invoice.weightPerBag *
            invoice.distanceInKilometers *
            fallbackRate;

        // Apply monetary rounding to fallback calculation
        invoiceAmount = MonetaryRounding.roundHalfUp(fallbackAmount);
        calculationMethod = 'Fallback Rate';
        fallbackRateUsedCount++;

        log('Using fallback rate for invoice ${invoice.tasNumber}: ${fallbackAmount.toStringAsFixed(2)} → rounded to: $invoiceAmount');
      }

      totalAmount += invoiceAmount;
      invoiceAmounts[invoice.tasNumber] = invoiceAmount;
      calculationDetails[invoice.tasNumber] = calculationMethod;
    }

    // Apply monetary rounding to the total amount
    final roundedTotalAmount = MonetaryRounding.roundHalfUp(totalAmount);

    log('Batch calculation complete: $slabRateUsedCount invoices used slab rates, $fallbackRateUsedCount used fallback rate');
    log('Total amount: ${totalAmount.toStringAsFixed(2)} → rounded to: $roundedTotalAmount');

    return SlabCalculationResult(
      totalAmount: roundedTotalAmount,
      invoiceAmounts: invoiceAmounts,
      calculationDetails: calculationDetails,
      slabRateUsedCount: slabRateUsedCount,
      fallbackRateUsedCount: fallbackRateUsedCount,
    );
  }

  /// Get available rate types for a specific district and date
  Future<List<String>> getAvailableRateTypes({
    required String districtId,
    required DateTime date,
  }) async {
    try {
      final result = await getActiveSlabsForDistrictUseCase.call(
        districtId: districtId,
        date: date,
      );

      return result.fold(
        (failure) => ['hmtRate', 'nonFuelRate'], // Default rate types
        (slabs) {
          if (slabs.isEmpty) return ['hmtRate', 'nonFuelRate'];

          final slab = slabs.first;
          final rate = slab.getRateForDistrict(districtId);

          if (rate == null) return ['hmtRate', 'nonFuelRate'];

          // Return standard rates plus custom columns
          final rateTypes = ['hmtRate', 'nonFuelRate'];
          rateTypes.addAll(rate.customColumns.keys);

          return rateTypes;
        },
      );
    } catch (e) {
      log('Error getting available rate types: $e');
      return ['hmtRate', 'nonFuelRate'];
    }
  }
}

/// Result class for batch slab calculations
class SlabCalculationResult {
  final double totalAmount;
  final Map<String, double> invoiceAmounts;
  final Map<String, String> calculationDetails;
  final int slabRateUsedCount;
  final int fallbackRateUsedCount;

  SlabCalculationResult({
    required this.totalAmount,
    required this.invoiceAmounts,
    required this.calculationDetails,
    required this.slabRateUsedCount,
    required this.fallbackRateUsedCount,
  });

  /// Get calculation summary
  String get summary {
    final total = slabRateUsedCount + fallbackRateUsedCount;
    return 'Total: ${totalAmount.toStringAsFixed(2)} | '
        'Slab rates: $slabRateUsedCount/$total | '
        'Fallback rates: $fallbackRateUsedCount/$total';
  }

  /// Check if all invoices used slab rates
  bool get allUsedSlabRates => fallbackRateUsedCount == 0;

  /// Check if any invoices used slab rates
  bool get anyUsedSlabRates => slabRateUsedCount > 0;
}
