import 'dart:developer';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

/// Service for validating calculation formulas
class FormulaValidationService {
  /// Validate a complete calculation formula
  static FormulaValidationResult validateFormula(
      CalculationFormulaModel formula) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic structure validation
    if (formula.formulaName.trim().isEmpty) {
      errors.add('Formula name is required');
    }

    if (formula.steps.isEmpty) {
      errors.add('At least one calculation step is required');
    }

    if (formula.finalResultVariable.trim().isEmpty) {
      errors.add('Final result variable must be specified');
    }

    // Validate steps
    final stepVariables = <String>{};
    for (int i = 0; i < formula.steps.length; i++) {
      final step = formula.steps[i];
      final stepValidation = validateFormulaStep(step, stepVariables.toList());

      if (stepValidation.hasErrors) {
        errors.addAll(
            stepValidation.errors.map((error) => 'Step ${i + 1}: $error'));
      }

      if (stepValidation.hasWarnings) {
        warnings.addAll(stepValidation.warnings
            .map((warning) => 'Step ${i + 1}: $warning'));
      }

      // Add this step's result variable to available variables for next steps
      if (step.resultVariable.trim().isNotEmpty) {
        stepVariables.add(step.resultVariable);
      }
    }

    // Validate final result variable exists
    if (formula.finalResultVariable.isNotEmpty) {
      final finalStepExists = formula.steps.any(
        (step) => step.resultVariable == formula.finalResultVariable,
      );
      if (!finalStepExists) {
        errors.add(
            'Final result variable "${formula.finalResultVariable}" does not exist in any step');
      }
    }

    // Check for duplicate result variables
    final resultVariables =
        formula.steps.map((step) => step.resultVariable).toList();
    final uniqueVariables = resultVariables.toSet();
    if (resultVariables.length != uniqueVariables.length) {
      errors.add(
          'Duplicate result variables found. Each step must have a unique result variable');
    }

    // Test formula execution if no errors
    Map<String, dynamic>? testResults;
    if (errors.isEmpty) {
      try {
        testResults =
            FlexibleFormulaCalculationService.testFormula(formula: formula);
        if (testResults['success'] != true) {
          errors.add('Formula execution test failed: ${testResults['error']}');
        }
      } catch (e) {
        errors.add('Formula execution test failed: $e');
      }
    }

    return FormulaValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      testResults: testResults,
    );
  }

  /// Validate a single formula step
  static StepValidationResult validateFormulaStep(
    FormulaStepModel step,
    List<String> availableStepVariables,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic field validation
    if (step.stepName.trim().isEmpty) {
      errors.add('Step name is required');
    }

    if (step.formula.trim().isEmpty) {
      errors.add('Formula is required');
    }

    if (step.resultVariable.trim().isEmpty) {
      errors.add('Result variable is required');
    }

    // Validate result variable name
    if (step.resultVariable.isNotEmpty) {
      if (!_isValidVariableName(step.resultVariable)) {
        errors.add(
            'Result variable "${step.resultVariable}" is not a valid variable name');
      }

      // Check if result variable conflicts with base variables
      if (FormulaVariables.baseVariables.contains(step.resultVariable)) {
        errors.add(
            'Result variable "${step.resultVariable}" conflicts with a base variable');
      }
    }

    // Validate formula syntax and variables
    if (step.formula.isNotEmpty) {
      final formulaValidation = validateFormulaExpression(
        step.formula,
        availableStepVariables,
      );

      errors.addAll(formulaValidation.errors);
      warnings.addAll(formulaValidation.warnings);
    }

    return StepValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate a formula expression
  static ExpressionValidationResult validateFormulaExpression(
    String expression,
    List<String> availableStepVariables,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    if (expression.trim().isEmpty) {
      errors.add('Expression cannot be empty');
      return ExpressionValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
      );
    }

    // Check for balanced parentheses
    if (!_hasBalancedParentheses(expression)) {
      errors.add('Unbalanced parentheses in expression');
    }

    // Extract variables from expression
    final usedVariables = _extractVariables(expression);
    final allAvailableVariables = [
      ...FormulaVariables.baseVariables,
      ...availableStepVariables,
    ];

    // Check if all used variables are available
    for (final variable in usedVariables) {
      if (!allAvailableVariables.contains(variable)) {
        errors.add('Unknown variable: $variable');
      }
    }

    // Check for unused available variables (warning only)
    final unusedVariables = allAvailableVariables
        .where((variable) => !usedVariables.contains(variable))
        .toList();

    if (unusedVariables.isNotEmpty && usedVariables.isNotEmpty) {
      warnings
          .add('Available but unused variables: ${unusedVariables.join(', ')}');
    }

    // Test expression syntax with dummy values
    if (errors.isEmpty) {
      try {
        String testExpression = expression;
        for (final variable in allAvailableVariables) {
          testExpression = testExpression.replaceAll(variable, '1.0');
        }

        // Convert operators before evaluation
        testExpression =
            testExpression.replaceAll('×', '*').replaceAll('÷', '/');

        final result = FlexibleFormulaCalculationService.evaluateExpression(
            testExpression);
        if (result == null) {
          errors.add('Invalid mathematical expression');
        }
      } catch (e) {
        errors.add('Expression syntax error: $e');
      }
    }

    return ExpressionValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      usedVariables: usedVariables,
      unusedVariables: unusedVariables,
    );
  }

  /// Check if parentheses are balanced
  static bool _hasBalancedParentheses(String expression) {
    int count = 0;
    for (int i = 0; i < expression.length; i++) {
      if (expression[i] == '(') count++;
      if (expression[i] == ')') count--;
      if (count < 0) return false; // Closing before opening
    }
    return count == 0;
  }

  /// Extract variable names from expression
  static List<String> _extractVariables(String expression) {
    final variables = <String>[];
    final allVariables = FormulaVariables.baseVariables;

    for (final variable in allVariables) {
      if (expression.contains(variable)) {
        variables.add(variable);
      }
    }

    // Extract step variables (step1, step2, etc.)
    final stepVariableRegex = RegExp(r'step\d+');
    final stepMatches = stepVariableRegex.allMatches(expression);
    for (final match in stepMatches) {
      final stepVariable = match.group(0)!;
      if (!variables.contains(stepVariable)) {
        variables.add(stepVariable);
      }
    }

    return variables;
  }

  /// Check if a variable name is valid
  static bool _isValidVariableName(String name) {
    // Variable names should start with a letter and contain only letters, numbers, and underscores
    final regex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$');
    return regex.hasMatch(name);
  }

  /// Get suggestions for fixing common formula errors
  static List<String> getSuggestions(FormulaValidationResult validation) {
    final suggestions = <String>[];

    for (final error in validation.errors) {
      if (error.contains('Unknown variable')) {
        suggestions.add(
            'Check variable spelling and ensure all variables are defined');
      } else if (error.contains('Unbalanced parentheses')) {
        suggestions.add(
            'Ensure each opening parenthesis "(" has a matching closing parenthesis ")"');
      } else if (error.contains('Invalid mathematical expression')) {
        suggestions.add('Check for proper mathematical operators and syntax');
      } else if (error.contains('Duplicate result variables')) {
        suggestions.add('Use unique names for each step\'s result variable');
      }
    }

    if (suggestions.isEmpty && validation.warnings.isNotEmpty) {
      suggestions
          .add('Consider addressing the warnings to improve formula clarity');
    }

    return suggestions;
  }
}

/// Result of formula validation
class FormulaValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<String, dynamic>? testResults;

  FormulaValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    this.testResults,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;
}

/// Result of step validation
class StepValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  StepValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;
}

/// Result of expression validation
class ExpressionValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final List<String> usedVariables;
  final List<String> unusedVariables;

  ExpressionValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    this.usedVariables = const [],
    this.unusedVariables = const [],
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasIssues => hasErrors || hasWarnings;
}
