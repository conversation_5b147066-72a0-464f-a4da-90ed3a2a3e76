import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/features/finance/bills/presentation/controllers/bill_detail_controller.dart';
import 'package:logestics/models/finance/bill_model.dart';

void main() {
  group('Bill Detail Integration Tests', () {
    late BillDetailController controller;
    late BillModel testBill;

    setUp(() {
      controller = BillDetailController();
      testBill = _createTestBill();
    });

    test('should initialize bill correctly', () {
      // Test bill initialization
      controller.initializeBill(testBill);
      
      expect(controller.currentBill, equals(testBill));
      expect(controller.currentBill?.billNumber, equals('001-B'));
      expect(controller.currentBill?.numberOfLinkedInvoices, equals(2));
    });

    test('should handle empty linked invoices list', () {
      // Test with bill that has no linked invoices
      final emptyBill = BillModel(
        billId: 'empty-bill-id',
        billNumber: '002-B',
        billDate: DateTime.now(),
        totalAmount: 0.0,
        numberOfLinkedInvoices: 0,
        billStatus: 'Pending',
        linkedInvoiceIds: [],
        companyUid: 'test-company-uid',
      );

      controller.initializeBill(emptyBill);
      
      expect(controller.currentBill?.linkedInvoiceIds, isEmpty);
      expect(controller.currentBill?.numberOfLinkedInvoices, equals(0));
    });

    test('should format monetary amounts correctly', () {
      // Test the private _formatMonetaryAmount method through Excel export
      controller.initializeBill(testBill);
      
      // The method should handle various amount formats
      expect(controller.currentBill?.totalAmount, equals(5000.0));
    });

    test('should handle bill with multiple linked invoices', () {
      // Test with bill that has multiple linked invoices
      final multiBill = BillModel(
        billId: 'multi-bill-id',
        billNumber: '003-B',
        billDate: DateTime.now(),
        totalAmount: 15000.0,
        numberOfLinkedInvoices: 5,
        billStatus: 'Pending',
        linkedInvoiceIds: ['TAS001', 'TAS002', 'TAS003', 'TAS004', 'TAS005'],
        companyUid: 'test-company-uid',
      );

      controller.initializeBill(multiBill);
      
      expect(controller.currentBill?.linkedInvoiceIds.length, equals(5));
      expect(controller.currentBill?.numberOfLinkedInvoices, equals(5));
      expect(controller.currentBill?.totalAmount, equals(15000.0));
    });

    test('should handle bill status correctly', () {
      // Test different bill statuses
      final completedBill = BillModel(
        billId: 'completed-bill-id',
        billNumber: '004-B',
        billDate: DateTime.now(),
        totalAmount: 8000.0,
        numberOfLinkedInvoices: 3,
        billStatus: 'Completed',
        linkedInvoiceIds: ['TAS006', 'TAS007', 'TAS008'],
        companyUid: 'test-company-uid',
      );

      controller.initializeBill(completedBill);
      
      expect(controller.currentBill?.billStatus, equals('Completed'));
    });

    test('should handle Excel export preparation', () {
      // Test that the controller can prepare for Excel export
      controller.initializeBill(testBill);
      
      // Verify that the bill is properly initialized for export
      expect(controller.currentBill, isNotNull);
      expect(controller.currentBill?.billNumber, isNotEmpty);
      expect(controller.currentBill?.linkedInvoiceIds, isNotEmpty);
    });

    test('should handle controller cleanup', () {
      // Test controller cleanup
      controller.initializeBill(testBill);
      expect(controller.currentBill, isNotNull);
      
      controller.onClose();
      
      // After cleanup, linkedInvoices should be cleared
      expect(controller.linkedInvoices, isEmpty);
      expect(controller.currentBill, isNull);
    });

    tearDown(() {
      controller.onClose();
    });
  });
}

// Helper method to create test bill
BillModel _createTestBill() {
  return BillModel(
    billId: 'test-bill-id',
    billNumber: '001-B',
    billDate: DateTime.now(),
    totalAmount: 5000.0,
    numberOfLinkedInvoices: 2,
    billStatus: 'Pending',
    linkedInvoiceIds: ['TAS001', 'TAS002'],
    companyUid: 'test-company-uid',
  );
}
