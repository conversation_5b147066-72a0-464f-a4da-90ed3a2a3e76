/// Constants and utilities for formula variables and operations
class FormulaVariables {
  // Available variables from invoice data
  static const String numberOfBags = 'numberOfBags';
  static const String weightPerBag = 'weightPerBag';
  static const String totalWeightKg = 'totalWeightKg';
  static const String totalWeightTons = 'totalWeightTons';
  static const String distanceInKilometers = 'distanceInKilometers';
  // Removed rateValue - will be replaced by dynamic custom columns

  // Step result variables (dynamic based on formula steps)
  static const String stepPrefix = 'step';

  /// Get all available base variables (excluding rateValue)
  static List<String> get baseVariables => [
        numberOfBags,
        weightPerBag,
        totalWeightKg,
        totalWeightTons,
        distanceInKilometers,
        // rateValue removed - now using dynamic custom columns only
      ];

  /// Get dynamic variables based on custom columns
  static List<String> getCustomColumnVariables(List<String> customColumns) {
    return customColumns.map((column) => sanitizeColumnName(column)).toList();
  }

  /// Get all available variables including custom columns
  static List<String> getAllVariables(List<String> customColumns) {
    return [
      ...baseVariables,
      ...getCustomColumnVariables(customColumns),
    ];
  }

  /// Sanitize column name to be a valid variable name
  static String sanitizeColumnName(String columnName) {
    // Convert to camelCase and remove special characters
    return columnName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-zA-Z0-9]'), '')
        .replaceFirstMapped(RegExp(r'^[a-z]'), (match) => match.group(0)!);
  }

  /// Get variable descriptions for UI (base variables only)
  static Map<String, String> get variableDescriptions => {
        numberOfBags: 'Number of bags from invoice',
        weightPerBag: 'Weight per bag from invoice (kg)',
        totalWeightKg:
            'Total weight in kilograms (numberOfBags × weightPerBag)',
        totalWeightTons: 'Total weight in tons (totalWeightKg ÷ 1000)',
        distanceInKilometers: 'Distance in kilometers from invoice',
      };

  /// Get variable display names for UI (base variables only)
  static Map<String, String> get variableDisplayNames => {
        numberOfBags: 'Number of Bags',
        weightPerBag: 'Weight per Bag (kg)',
        totalWeightKg: 'Total Weight (kg)',
        totalWeightTons: 'Total Weight (tons)',
        distanceInKilometers: 'Distance (km)',
      };

  /// Get display names for custom column variables
  static Map<String, String> getCustomColumnDisplayNames(
      List<String> customColumns) {
    final Map<String, String> displayNames = {};
    for (final column in customColumns) {
      final variableName = sanitizeColumnName(column);
      displayNames[variableName] =
          column; // Use original column name as display
    }
    return displayNames;
  }

  /// Get all variable descriptions including custom columns
  static Map<String, String> getAllVariableDescriptions(
      List<String> customColumns) {
    final Map<String, String> allDescriptions = Map.from(variableDescriptions);
    for (final column in customColumns) {
      final variableName = sanitizeColumnName(column);
      allDescriptions[variableName] = 'Custom column: $column';
    }
    return allDescriptions;
  }

  /// Get all variable display names including custom columns
  static Map<String, String> getAllVariableDisplayNames(
      List<String> customColumns) {
    final Map<String, String> allDisplayNames = Map.from(variableDisplayNames);
    allDisplayNames.addAll(getCustomColumnDisplayNames(customColumns));
    return allDisplayNames;
  }

  /// Check if a variable name is valid
  static bool isValidVariable(String variableName,
      [List<String>? customColumns]) {
    if (baseVariables.contains(variableName)) return true;
    if (variableName.startsWith(stepPrefix)) {
      // Check if it's a valid step variable (e.g., step1, step2)
      final stepNumber = variableName.substring(stepPrefix.length);
      return int.tryParse(stepNumber) != null;
    }
    // Check if it's a valid custom column variable
    if (customColumns != null) {
      final customVariables = getCustomColumnVariables(customColumns);
      if (customVariables.contains(variableName)) return true;
    }
    return false;
  }

  /// Get step variable name for a given step number
  static String getStepVariable(int stepNumber) {
    return '$stepPrefix$stepNumber';
  }

  /// Extract step number from step variable name
  static int? getStepNumber(String stepVariable) {
    if (!stepVariable.startsWith(stepPrefix)) return null;
    final numberPart = stepVariable.substring(stepPrefix.length);
    return int.tryParse(numberPart);
  }
}

/// Supported mathematical operations
class FormulaOperations {
  static const String addition = '+';
  static const String subtraction = '-';
  static const String multiplication = '×';
  static const String division = '÷';
  static const String openParenthesis = '(';
  static const String closeParenthesis = ')';

  /// Get all supported operations
  static List<String> get allOperations => [
        addition,
        subtraction,
        multiplication,
        division,
        openParenthesis,
        closeParenthesis,
      ];

  /// Get operation descriptions for UI
  static Map<String, String> get operationDescriptions => {
        addition: 'Addition',
        subtraction: 'Subtraction',
        multiplication: 'Multiplication',
        division: 'Division',
        openParenthesis: 'Open Parenthesis',
        closeParenthesis: 'Close Parenthesis',
      };

  /// Convert display operations to calculation operations
  static String convertToCalculationOperator(String displayOperator) {
    switch (displayOperator) {
      case multiplication:
        return '*';
      case division:
        return '/';
      default:
        return displayOperator;
    }
  }

  /// Convert calculation operations to display operations
  static String convertToDisplayOperator(String calculationOperator) {
    switch (calculationOperator) {
      case '*':
        return multiplication;
      case '/':
        return division;
      default:
        return calculationOperator;
    }
  }

  /// Check if a character is a valid operation
  static bool isValidOperation(String operation) {
    return allOperations.contains(operation) ||
        operation == '*' ||
        operation == '/';
  }
}

/// Default formula templates with documentation and examples
class FormulaTemplates {
  /// Get the basic calculation formula
  static Map<String, dynamic> get standardFormula => {
        'formulaId': 'basic_formula',
        'formulaName': 'Basic Calculation',
        'description':
            'Basic calculation: Total Tons × Distance\n\nAdd custom columns to create more complex calculations.',
        'steps': [
          {
            'stepId': 'step1',
            'stepName': 'Calculate Basic Amount',
            'formula': 'totalWeightTons × distanceInKilometers',
            'resultVariable': 'finalAmount',
            'description':
                'Standard billing calculation: tons × kilometers × rate',
          }
        ],
        'finalResultVariable': 'finalAmount',
        'isActive': true,
      };

  /// Get multi-step calculation example
  static Map<String, dynamic> get multiStepFormula => {
        'formulaId': 'multi_step_formula',
        'formulaName': 'Multi-Step Calculation',
        'description':
            'Example: Calculate weight rate first, then final amount\n\nThis demonstrates how to break calculations into multiple steps for better clarity and flexibility.',
        'steps': [
          {
            'stepId': 'step1',
            'stepName': 'Calculate Weight Rate',
            'formula': 'distanceInKilometers × 1.0',
            'resultVariable': 'weightRate',
            'description':
                'Distance multiplied by factor (replace 1.0 with custom column variable)',
          },
          {
            'stepId': 'step2',
            'stepName': 'Calculate Final Amount',
            'formula': 'totalWeightTons × weightRate',
            'resultVariable': 'finalAmount',
            'description':
                'Total tons multiplied by weight rate for final amount',
          }
        ],
        'finalResultVariable': 'finalAmount',
        'isActive': true,
      };

  /// Get markup calculation formula
  static Map<String, dynamic> get markupFormula => {
        'formulaId': 'markup_formula',
        'formulaName': 'Standard with 10% Markup',
        'description':
            'Standard calculation with 10% markup applied\n\nUseful for adding profit margins or handling additional costs.',
        'steps': [
          {
            'stepId': 'step1',
            'stepName': 'Calculate Base Amount',
            'formula': 'totalWeightTons × distanceInKilometers',
            'resultVariable': 'baseAmount',
            'description': 'Basic calculation using weight and distance',
          },
          {
            'stepId': 'step2',
            'stepName': 'Apply 10% Markup',
            'formula': 'baseAmount × 1.1',
            'resultVariable': 'finalAmount',
            'description': 'Add 10% markup to base amount',
          }
        ],
        'finalResultVariable': 'finalAmount',
        'isActive': true,
      };

  /// Get weight-based calculation formula
  static Map<String, dynamic> get weightBasedFormula => {
        'formulaId': 'weight_based_formula',
        'formulaName': 'Weight-Based Calculation',
        'description':
            'Calculate weight in tons first, then apply distance and rate\n\nUseful when you need to apply different logic based on weight calculations.',
        'steps': [
          {
            'stepId': 'step1',
            'stepName': 'Calculate Weight in Tons',
            'formula': '(numberOfBags × weightPerBag) ÷ 1000',
            'resultVariable': 'calculatedTons',
            'description': 'Convert total weight from kg to tons',
          },
          {
            'stepId': 'step2',
            'stepName': 'Calculate Final Amount',
            'formula': 'calculatedTons × distanceInKilometers',
            'resultVariable': 'finalAmount',
            'description':
                'Apply distance to calculated weight (add custom rate column as needed)',
          }
        ],
        'finalResultVariable': 'finalAmount',
        'isActive': true,
      };

  /// Get complex multi-step formula
  static Map<String, dynamic> get complexFormula => {
        'formulaId': 'complex_formula',
        'formulaName': 'Complex Multi-Step Calculation',
        'description':
            'Advanced example with multiple calculation steps\n\nDemonstrates how to build complex formulas with intermediate calculations.',
        'steps': [
          {
            'stepId': 'step1',
            'stepName': 'Calculate Base Weight Rate',
            'formula': 'totalWeightTons × 1.0',
            'resultVariable': 'baseWeightRate',
            'description':
                'Base calculation based on weight (replace 1.0 with custom rate column)',
          },
          {
            'stepId': 'step2',
            'stepName': 'Calculate Distance Factor',
            'formula': 'distanceInKilometers × 0.1',
            'resultVariable': 'distanceFactor',
            'description': 'Distance-based adjustment factor',
          },
          {
            'stepId': 'step3',
            'stepName': 'Apply Distance Adjustment',
            'formula': 'baseWeightRate + distanceFactor',
            'resultVariable': 'adjustedRate',
            'description': 'Add distance factor to base rate',
          },
          {
            'stepId': 'step4',
            'stepName': 'Calculate Final Amount',
            'formula': 'adjustedRate × distanceInKilometers',
            'resultVariable': 'finalAmount',
            'description': 'Final calculation with adjusted rate',
          }
        ],
        'finalResultVariable': 'finalAmount',
        'isActive': true,
      };

  /// Get all available templates
  static List<Map<String, dynamic>> get allTemplates => [
        standardFormula,
        multiStepFormula,
        markupFormula,
        weightBasedFormula,
        complexFormula,
      ];

  /// Get template by ID
  static Map<String, dynamic>? getTemplateById(String templateId) {
    try {
      return allTemplates
          .firstWhere((template) => template['formulaId'] == templateId);
    } catch (e) {
      return null;
    }
  }

  /// Get formula documentation
  static String get documentation => '''
# Formula Builder Documentation

## Overview
The Formula Builder allows you to create custom calculation formulas for billing amounts instead of using the hardcoded calculation logic.

## Available Variables
- **numberOfBags**: Number of bags from invoice
- **weightPerBag**: Weight per bag from invoice (kg)
- **totalWeightKg**: Total weight in kilograms (numberOfBags × weightPerBag)
- **totalWeightTons**: Total weight in tons (totalWeightKg ÷ 1000)
- **distanceInKilometers**: Distance in kilometers from invoice
- **Custom Columns**: User-defined columns from slab configuration (e.g., hmtRate, nonFuelRate, WHT, GST)

## Step Variables
You can use results from previous steps in later steps:
- **step1**: Result from step 1
- **step2**: Result from step 2
- And so on...

## Supported Operations
- **Addition**: +
- **Subtraction**: -
- **Multiplication**: ×
- **Division**: ÷
- **Parentheses**: ( )

## Formula Examples

### Basic Formula
`totalWeightTons × distanceInKilometers × customRateColumn`

### With Markup
Step 1: `totalWeightTons × distanceInKilometers × customRateColumn = baseAmount`
Step 2: `baseAmount × 1.1 = finalAmount` (10% markup)

### Complex Calculation
Step 1: `(numberOfBags × weightPerBag) ÷ 1000 = calculatedTons`
Step 2: `calculatedTons × distanceInKilometers = tonKm`
Step 3: `tonKm × customRateColumn × 1.05 = finalAmount` (5% markup)

## Best Practices
1. Use descriptive step names
2. Break complex calculations into multiple steps
3. Test your formulas before saving
4. Use parentheses to ensure correct order of operations
5. Keep formulas as simple as possible while meeting your needs

## Tips
- Start with a template and modify it to your needs
- Use the Test Formula button to verify your calculations
- Check the Variable Reference for available variables
- Each step must have a unique result variable name
''';

  /// Get quick reference guide
  static String get quickReference => '''
# Quick Reference

## Common Patterns
- Standard: `totalWeightTons × distanceInKilometers × customRateColumn`
- With markup: `baseAmount × 1.1` (10% markup)
- Weight calculation: `(numberOfBags × weightPerBag) ÷ 1000`
- Distance factor: `distanceInKilometers × 0.1`

## Variables
- numberOfBags, weightPerBag, totalWeightKg, totalWeightTons
- distanceInKilometers, customColumns (user-defined)
- step1, step2, step3... (from previous steps)

## Operations
- + (add), - (subtract), × (multiply), ÷ (divide)
- ( ) for grouping operations
''';
}
