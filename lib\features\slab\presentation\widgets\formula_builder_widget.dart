import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logestics/main.dart';
import 'package:provider/provider.dart';
import 'package:logestics/core/utils/app_constants/styles/app_text_styles.dart';
import 'package:logestics/core/utils/widgets/my_text_field.dart';
import 'package:logestics/core/utils/widgets/custom_button.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

class FormulaBuilderWidget extends StatefulWidget {
  final CalculationFormulaModel? initialFormula;
  final Function(CalculationFormulaModel?) onFormulaChanged;
  final bool readOnly;
  final List<String> customColumns;

  const FormulaBuilderWidget({
    super.key,
    this.initialFormula,
    required this.onFormulaChanged,
    this.readOnly = false,
    this.customColumns = const [],
  });

  @override
  State<FormulaBuilderWidget> createState() => _FormulaBuilderWidgetState();
}

class _FormulaBuilderWidgetState extends State<FormulaBuilderWidget> {
  late TextEditingController formulaNameController;
  late TextEditingController formulaDescriptionController;
  List<FormulaStepModel> steps = [];
  String finalResultVariable = '';
  bool isTestingFormula = false;
  Map<String, dynamic>? testResults;

  // Controllers for step fields
  Map<int, TextEditingController> stepNameControllers = {};
  Map<int, TextEditingController> stepFormulaControllers = {};
  Map<int, TextEditingController> stepResultControllers = {};
  Map<int, TextEditingController> stepDescriptionControllers = {};

  @override
  void initState() {
    super.initState();
    formulaNameController = TextEditingController();
    formulaDescriptionController = TextEditingController();

    if (widget.initialFormula != null) {
      _loadFormula(widget.initialFormula!);
    } else {
      _initializeWithDefaultStep();
    }
  }

  @override
  void dispose() {
    formulaNameController.dispose();
    formulaDescriptionController.dispose();
    _disposeStepControllers();
    super.dispose();
  }

  void _disposeStepControllers() {
    for (var controller in stepNameControllers.values) {
      controller.dispose();
    }
    for (var controller in stepFormulaControllers.values) {
      controller.dispose();
    }
    for (var controller in stepResultControllers.values) {
      controller.dispose();
    }
    for (var controller in stepDescriptionControllers.values) {
      controller.dispose();
    }
    stepNameControllers.clear();
    stepFormulaControllers.clear();
    stepResultControllers.clear();
    stepDescriptionControllers.clear();
  }

  void _loadFormula(CalculationFormulaModel formula) {
    formulaNameController.text = formula.formulaName;
    formulaDescriptionController.text = formula.description ?? '';
    steps = List.from(formula.steps);
    finalResultVariable = formula.finalResultVariable;
    _initializeStepControllers();
  }

  void _initializeWithDefaultStep() {
    steps = [
      FormulaStepModel(
        stepId: 'step1',
        stepName: 'Calculate Final Amount',
        formula: 'totalWeightTons × distanceInKilometers',
        resultVariable: 'finalAmount',
        description: 'Basic calculation - add custom columns as needed',
      ),
    ];
    finalResultVariable = 'finalAmount';
    formulaNameController.text = 'Basic Calculation';
    formulaDescriptionController.text =
        'Basic calculation using weight and distance - customize with your custom columns';
    _initializeStepControllers();
  }

  void _initializeStepControllers() {
    _disposeStepControllers();

    for (int i = 0; i < steps.length; i++) {
      final step = steps[i];
      stepNameControllers[i] = TextEditingController(text: step.stepName);
      stepFormulaControllers[i] = TextEditingController(text: step.formula);
      stepResultControllers[i] =
          TextEditingController(text: step.resultVariable);
      stepDescriptionControllers[i] =
          TextEditingController(text: step.description ?? '');
    }
  }

  @override
  Widget build(BuildContext context) {
    notifier = Provider.of(context, listen: true);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notifier.getBgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: notifier.getfillborder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildFormulaInfo(),
          const SizedBox(height: 16),
          _buildStepsSection(),
          const SizedBox(height: 16),
          _buildFinalResultSection(),
          const SizedBox(height: 16),
          _buildActionButtons(),
          if (testResults != null) ...[
            const SizedBox(height: 16),
            _buildTestResults(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.calculate, color: notifier.text),
        const SizedBox(width: 8),
        Text(
          'Calculation Formula',
          style: AppTextStyles.invoiceHeaderStyle.copyWith(
            color: notifier.text,
            fontSize: 16,
          ),
        ),
        const Spacer(),
        if (!widget.readOnly)
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: notifier.text),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'load_template',
                child: Text('Load Template'),
              ),
              const PopupMenuItem(
                value: 'reset',
                child: Text('Reset to Standard'),
              ),
              const PopupMenuItem(
                value: 'clear',
                child: Text('Clear Formula'),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildFormulaInfo() {
    return Column(
      children: [
        MyTextFormField(
          titleText: 'Formula Name',
          labelText: 'Enter formula name',
          hintText: 'e.g., Standard Calculation',
          controller: formulaNameController,
          readOnly: widget.readOnly,
          onChanged: (_) => _updateFormula(),
        ),
        const SizedBox(height: 12),
        MyTextFormField(
          titleText: 'Description (Optional)',
          labelText: 'Enter formula description',
          hintText: 'e.g., Standard billing calculation formula',
          controller: formulaDescriptionController,
          readOnly: widget.readOnly,
          onChanged: (_) => _updateFormula(),
        ),
      ],
    );
  }

  Widget _buildStepsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Calculation Steps',
              style: AppTextStyles.invoiceHeaderStyle.copyWith(
                color: notifier.text,
                fontSize: 14,
              ),
            ),
            const Spacer(),
            if (!widget.readOnly)
              ElevatedButton.icon(
                onPressed: _addStep,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Add Step'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xff0f79f3),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        ...steps.asMap().entries.map((entry) {
          final index = entry.key;
          final step = entry.value;
          return _buildStepCard(step, index);
        }),
      ],
    );
  }

  Widget _buildStepCard(FormulaStepModel step, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: notifier.getHoverColor,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Step ${index + 1}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: notifier.text,
                  ),
                ),
                const Spacer(),
                if (!widget.readOnly)
                  IconButton(
                    onPressed: () => _removeStep(index),
                    icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            _buildStepEditor(step, index),
          ],
        ),
      ),
    );
  }

  Widget _buildStepEditor(FormulaStepModel step, int index) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: stepNameControllers[index],
                decoration: const InputDecoration(
                  labelText: 'Step Name',
                  border: OutlineInputBorder(),
                ),
                readOnly: widget.readOnly,
                onChanged: (value) => _updateStep(index, stepName: value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: stepResultControllers[index],
                decoration: const InputDecoration(
                  labelText: 'Result Variable',
                  border: OutlineInputBorder(),
                ),
                readOnly: widget.readOnly,
                onChanged: (value) => _updateStep(index, resultVariable: value),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildFormulaEditor(step, index),
        const SizedBox(height: 8),
        TextFormField(
          controller: stepDescriptionControllers[index],
          decoration: const InputDecoration(
            labelText: 'Description (Optional)',
            border: OutlineInputBorder(),
          ),
          readOnly: widget.readOnly,
          onChanged: (value) => _updateStep(index, description: value),
        ),
      ],
    );
  }

  Widget _buildFormulaEditor(FormulaStepModel step, int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: stepFormulaControllers[index],
          decoration: const InputDecoration(
            labelText: 'Formula',
            border: OutlineInputBorder(),
            hintText:
                'e.g., totalWeightTons × distanceInKilometers × rateValue',
          ),
          readOnly: widget.readOnly,
          onChanged: (value) => _updateStep(index, formula: value),
        ),
        const SizedBox(height: 8),
        if (!widget.readOnly) _buildVariableButtons(index),
      ],
    );
  }

  Widget _buildVariableButtons(int stepIndex) {
    // Get display names for all variables including custom columns
    final allDisplayNames =
        FormulaVariables.getAllVariableDisplayNames(widget.customColumns);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Variables:',
          style: TextStyle(
            fontSize: 12,
            color: notifier.text.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 4),
        if (widget.customColumns.isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              'No custom columns available. Add custom columns to use as variables.',
              style: TextStyle(
                fontSize: 11,
                color: notifier.text.withValues(alpha: 0.5),
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        Wrap(
          spacing: 4,
          runSpacing: 4,
          children: [
            // Base variables (excluding rateValue)
            ...FormulaVariables.baseVariables.map((variable) =>
                _buildVariableChip(variable, stepIndex, allDisplayNames)),
            // Custom column variables only
            ...FormulaVariables.getCustomColumnVariables(widget.customColumns)
                .map((variable) =>
                    _buildVariableChip(variable, stepIndex, allDisplayNames)),
            // Operations
            ...FormulaOperations.allOperations
                .map((operation) => _buildOperationChip(operation, stepIndex)),
          ],
        ),
      ],
    );
  }

  Widget _buildVariableChip(
      String variable, int stepIndex, Map<String, String> displayNames) {
    return ActionChip(
      label: Text(
        displayNames[variable] ?? variable,
        style: const TextStyle(fontSize: 10),
      ),
      onPressed: () => _insertIntoFormula(stepIndex, variable),
      backgroundColor: Colors.blue.withValues(alpha: 0.1),
    );
  }

  Widget _buildOperationChip(String operation, int stepIndex) {
    return ActionChip(
      label: Text(operation, style: const TextStyle(fontSize: 12)),
      onPressed: () => _insertIntoFormula(stepIndex, ' $operation '),
      backgroundColor: Colors.green.withValues(alpha: 0.1),
    );
  }

  Widget _buildFinalResultSection() {
    final availableVariables =
        steps.map((step) => step.resultVariable).toList();

    return Row(
      children: [
        Text(
          'Final Result Variable:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: notifier.text,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: DropdownButtonFormField<String>(
            value: availableVariables.contains(finalResultVariable)
                ? finalResultVariable
                : null,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            items: availableVariables.map((variable) {
              return DropdownMenuItem(
                value: variable,
                child: Text(variable),
              );
            }).toList(),
            onChanged: widget.readOnly
                ? null
                : (value) {
                    setState(() {
                      finalResultVariable = value ?? '';
                      _updateFormula();
                    });
                  },
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (!widget.readOnly) ...[
          ElevatedButton.icon(
            onPressed: _testFormula,
            icon: isTestingFormula
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.play_arrow),
            label: Text(isTestingFormula ? 'Testing...' : 'Test Formula'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
        ],
        ElevatedButton.icon(
          onPressed: _showVariableReference,
          icon: const Icon(Icons.help_outline),
          label: const Text('Variable Reference'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildTestResults() {
    final results = testResults!;
    final success = results['success'] as bool;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: success
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: success ? Colors.green : Colors.red,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                success ? Icons.check_circle : Icons.error,
                color: success ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                success ? 'Formula Test Successful' : 'Formula Test Failed',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: success ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (success) ...[
            Text('Final Result: ${results['finalResult']}'),
            const SizedBox(height: 8),
            Text('Step Details:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            ...((results['stepDetails'] as List).map((detail) => Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('${detail['stepName']}: ${detail['result']}'),
                ))),
          ] else ...[
            Text('Error: ${results['error']}',
                style: TextStyle(color: Colors.red)),
          ],
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'load_template':
        _showTemplateDialog();
        break;
      case 'reset':
        _initializeWithDefaultStep();
        _updateFormula();
        break;
      case 'clear':
        _clearFormula();
        break;
    }
  }

  void _showTemplateDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Load Formula Template'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: FormulaTemplates.allTemplates.map((template) {
            return ListTile(
              title: Text(template['formulaName']),
              subtitle: Text(template['description']),
              onTap: () {
                _loadTemplateFormula(template);
                Get.back();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _loadTemplateFormula(Map<String, dynamic> template) {
    final formula = CalculationFormulaModel.fromJson(template);
    _loadFormula(formula);
    setState(() {});
    _updateFormula();
  }

  void _clearFormula() {
    setState(() {
      formulaNameController.clear();
      formulaDescriptionController.clear();
      steps.clear();
      finalResultVariable = '';
      testResults = null;
    });
    widget.onFormulaChanged(null);
  }

  void _addStep() {
    setState(() {
      final stepNumber = steps.length + 1;
      steps.add(FormulaStepModel(
        stepId: 'step$stepNumber',
        stepName: 'Step $stepNumber',
        formula: '',
        resultVariable: 'step$stepNumber',
      ));
      _initializeStepControllers();
    });
    _updateFormula();
  }

  void _removeStep(int index) {
    setState(() {
      steps.removeAt(index);
      _initializeStepControllers();
    });
    _updateFormula();
  }

  void _updateStep(
    int index, {
    String? stepName,
    String? formula,
    String? resultVariable,
    String? description,
  }) {
    setState(() {
      steps[index] = steps[index].copyWith(
        stepName: stepName,
        formula: formula,
        resultVariable: resultVariable,
        description: description,
      );
    });
    _updateFormula();
  }

  void _insertIntoFormula(int stepIndex, String text) {
    final controller = stepFormulaControllers[stepIndex];
    if (controller != null) {
      final currentText = controller.text;
      final selection = controller.selection;

      // Insert text at cursor position
      final newText = currentText.replaceRange(
        selection.start,
        selection.end,
        text,
      );

      controller.text = newText;

      // Update cursor position
      final newCursorPosition = selection.start + text.length;
      controller.selection = TextSelection.collapsed(offset: newCursorPosition);

      // Update the step
      _updateStep(stepIndex, formula: newText);
    }
  }

  void _updateFormula() {
    if (formulaNameController.text.isEmpty ||
        steps.isEmpty ||
        finalResultVariable.isEmpty) {
      widget.onFormulaChanged(null);
      return;
    }

    final formula = CalculationFormulaModel(
      formulaId: DateTime.now().millisecondsSinceEpoch.toString(),
      formulaName: formulaNameController.text,
      description: formulaDescriptionController.text.isEmpty
          ? null
          : formulaDescriptionController.text,
      steps: steps,
      finalResultVariable: finalResultVariable,
    );

    widget.onFormulaChanged(formula);
  }

  void _testFormula() async {
    if (steps.isEmpty || finalResultVariable.isEmpty) {
      Get.snackbar('Error', 'Please complete the formula before testing');
      return;
    }

    setState(() {
      isTestingFormula = true;
      testResults = null;
    });

    final formula = CalculationFormulaModel(
      formulaId: 'test',
      formulaName: formulaNameController.text,
      steps: steps,
      finalResultVariable: finalResultVariable,
    );

    // Prepare test variables including custom columns
    final testVariables = <String, double>{
      FormulaVariables.numberOfBags: 100.0,
      FormulaVariables.weightPerBag: 50.0,
      FormulaVariables.totalWeightKg: 5000.0,
      FormulaVariables.totalWeightTons: 5.0,
      FormulaVariables.distanceInKilometers: 150.0,
    };

    // Add custom columns with sample values
    for (final column in widget.customColumns) {
      final variableName = FormulaVariables.sanitizeColumnName(column);
      // Use sample rate values for testing
      testVariables[variableName] = 2.5; // Default test value
    }

    final results = FlexibleFormulaCalculationService.testFormula(
      formula: formula,
      testVariables: testVariables,
    );

    setState(() {
      isTestingFormula = false;
      testResults = results;
    });
  }

  void _showVariableReference() {
    final allDescriptions =
        FormulaVariables.getAllVariableDescriptions(widget.customColumns);

    Get.dialog(
      AlertDialog(
        title: const Text('Variable Reference'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Available Variables:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              if (widget.customColumns.isEmpty)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    'No custom columns available. Add custom columns to use as variables in your formulas.',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ...allDescriptions.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: RichText(
                    text: TextSpan(
                      style: DefaultTextStyle.of(context).style,
                      children: [
                        TextSpan(
                          text: '${entry.key}: ',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        TextSpan(text: entry.value),
                      ],
                    ),
                  ),
                );
              }),
              const SizedBox(height: 16),
              const Text('Operations:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              ...FormulaOperations.operationDescriptions.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: RichText(
                    text: TextSpan(
                      style: DefaultTextStyle.of(context).style,
                      children: [
                        TextSpan(
                          text: '${entry.key}: ',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        TextSpan(text: entry.value),
                      ],
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
