Stack trace:
Frame         Function      Args
0007FFFF9B70  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B70, 0007FFFF8A70) msys-2.0.dll+0x1FE8E
0007FFFF9B70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E48) msys-2.0.dll+0x67F9
0007FFFF9B70  000210046832 (000210286019, 0007FFFF9A28, 0007FFFF9B70, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B70  000210068E24 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E50  00021006A225 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF6D900000 ntdll.dll
7FFF6C660000 KERNEL32.DLL
7FFF6AFC0000 KERNELBASE.dll
7FFF6D260000 USER32.dll
7FFF6B670000 win32u.dll
7FFF6D430000 GDI32.dll
7FFF6B3B0000 gdi32full.dll
7FFF6ADC0000 msvcp_win.dll
7FFF6AE70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF6D7F0000 advapi32.dll
7FFF6BCB0000 msvcrt.dll
7FFF6C4B0000 sechost.dll
7FFF6BD60000 RPCRT4.dll
7FFF69F60000 CRYPTBASE.DLL
7FFF6AA50000 bcryptPrimitives.dll
7FFF6C7C0000 IMM32.DLL
